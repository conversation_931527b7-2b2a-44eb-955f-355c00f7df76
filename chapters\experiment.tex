% 第四章内容 - 修正版
\chapter{实验结果与分析}

\section{实验环境}
本研究的实验环境配置如下：

硬件环境:
\begin{description}
  \item[CPU:] 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz
  \item[GPU:] NVIDIA GeForce RTX 3060 6GB
  \item[RAM:] 32GB DDR4
\end{description}

软件环境:
\begin{description}
  \item[操作系统:] Windows 11 Pro
  \item[Python 版本:] 3.10.9
  \item[主要库版本:] PyTorch 2.0.1, Scanpy 1.9.3, AnnData 0.9.1, scvi-tools 0.20.3
\end{description}

\section{数据集}

本研究使用了四个公开可用的单细胞多组学数据集进行算法评估。表\ref{tab:datasets_info}总结了各数据集的基本信息，包括细胞数量、基因数量、染色质区域数量和数据类型等关键特征。

\input{tables/table_1}

其中，neurips数据集是一个大规模的多批次单细胞多组学数据集，包含69,249个细胞，涵盖13个不同的批次（s1d1至s4d9）和22种细胞类型。该数据集的RNA模态包含13,431个基因，ATAC模态包含116,490个染色质可及性峰。neurips数据集的批次来源于不同的实验条件：不同供体（s1-s4）、不同时间点（d1-d10），为批次效应分析提供了丰富的实验设计。

\section{评估方法}
本研究采用了以下实验方法来评估MultiVI算法的性能：首先进行数据预处理，对原始数据进行质量控制、标准化和特征选择；然后进行模型训练，使用不同的超参数配置训练MultiVI模型；接着进行性能评估，使用多种指标评估模型在细胞聚类和批次效应校正方面的表现；最后进行算法对比，与其他主流算法进行对比分析。

\section{实验结果}

\subsection{模型训练与收敛}

MultiVI模型在不同数据集上的训练时间和收敛情况如下：10x\_lymph\_node的训练时间为26.8分钟，收敛轮数为315，最终验证损失为1.642；snare\_p0的训练时间为15.2分钟，收敛轮数为298，最终验证损失为2.047；paired\_cellline的训练时间为5.7分钟，收敛轮数为245，最终验证损失为1.523。

本研究发现，模型训练在RTX 3060 GPU上能够高效完成，相比CPU训练提高了显著的速度。通过参数优化分析，本研究确定了各数据集的最优聚类分辨率参数。

\subsection{参数优化分析}

在模型参数优化这个环节，我花费了相当多的时间来探索不同参数配置对结果的影响。最初我尝试了多种参数组合，但很快发现聚类分辨率参数对最终结果的影响最为显著，因此决定重点对这个参数进行系统性的优化。

我的策略是在一个相对宽泛的参数范围内进行网格搜索，对每个参数值都计算ARI、NMI和V-measure这三个关键指标，然后通过比较这些指标的变化趋势来确定最佳参数。

从图\ref{fig:10x_metrics}的结果来看，10x\_lymph\_node数据集的表现让我印象深刻。我注意到随着分辨率参数的增加，聚类数量确实在逐渐增多，但有趣的是聚类质量指标呈现出了一个典型的倒U型曲线——先上升然后缓慢下降。这种模式其实符合我的预期，因为分辨率过低会导致聚类过于粗糙，而分辨率过高则可能产生过度分割。最终确定的最优分辨率为1.0，此时ARI达到了0.854，这个结果让我比较满意。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/10x_lymph_node_metrics_vs_resolution.png}
\caption{10x淋巴结数据集聚类指标随分辨率参数的变化}
\label{fig:10x_metrics}
\end{figure}

paired\_cellline数据集的优化结果如图\ref{fig:paired_cellline_metrics}所示，这个数据集给了我一个惊喜。在分辨率1.1时，模型达到了最佳性能，ARI高达0.921，这个数值明显超出了我的预期。我分析认为这可能与该数据集的特殊性质有关——作为配对的细胞系数据，其细胞类型相对明确且异质性较低，这为MultiVI发挥其在配对数据处理方面的优势提供了理想的条件。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/paired_cellline_metrics_vs_resolution.png}
\caption{配对细胞系数据集聚类指标随分辨率参数的变化}
\label{fig:paired_cellline_metrics}
\end{figure}

snare\_p0数据集的结果如图\ref{fig:snare_metrics}所示，坦率地说，这个数据集是三个数据集中最具挑战性的。最优分辨率为1.3，ARI为0.756，虽然这个数值相对较低，但考虑到该数据集的特殊性，我认为这个结果是可以接受的。通过深入分析，我发现这个数据集的聚类难度确实较高，这主要源于其神经发育数据的内在复杂性——发育过程中的细胞往往处于连续的状态转换中，细胞类型边界相对模糊，这无疑增加了聚类算法的难度。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/snare_p0_metrics_vs_resolution.png}
\caption{SNARE-P0数据集聚类指标随分辨率参数的变化}
\label{fig:snare_metrics}
\end{figure}

通过参数优化分析，确定了不同数据集的最优聚类分辨率：10x\_lymph\_node数据集的最优分辨率为1.0，paired\_cellline数据集的最优分辨率为1.1，snare\_p0数据集的最优分辨率为1.3。这种参数优化方法确保了模型在不同数据集上都能达到最佳性能。

\subsection{细胞聚类与可视化分析}

在完成参数优化后，我对MultiVI在三个数据集上的整体聚类性能进行了全面评估。从最终的结果来看，不同数据集确实展现出了明显的性能差异：10x\_lymph\_node数据集的表现相当稳定，ARI为0.854，NMI为0.895，V-measure为0.871，三个指标都保持在较高水平；snare\_p0数据集的结果相对较低，ARI为0.756，NMI为0.800，V-measure为0.765，但考虑到其数据特性，这个结果仍然是合理的；而paired\_cellline数据集的表现最为出色，ARI达到0.921，NMI为0.945，V-measure为0.937，几乎所有指标都接近了理想值。

这种性能差异让我思考了不同数据集特征对算法表现的影响，我认为这不仅反映了MultiVI算法本身的特点，也揭示了不同类型单细胞数据在分析难度上的本质差异。

表\ref{tab:multivi_performance}详细展示了MultiVI在三个数据集上的聚类性能和训练指标评估结果。

\input{tables/table_3}

MultiVI在细胞类型聚类方面表现优异，能够准确识别和分离不同的细胞类型。无监督聚类结果与已知的细胞类型标注高度一致，验证了算法在细胞类型识别方面的准确性。

\subsection{批次效应校正}

由于10x\_lymph\_node、snare\_p0和paired\_cellline数据集在原理上不适合进行批次效应评估（缺乏真实的批次变异），本研究使用neurips数据集进行批次效应校正的专门评估。neurips数据集包含来自不同实验条件和时间点的多个批次，为批次效应分析提供了理想的测试环境。

本研究选择了三个具有代表性的批次组合进行分析：

\textbf{批次组合1（s1d1\_s1d2\_s1d3）}：来自同一供体不同时间点的批次，包含17,243个细胞。MultiVI在该组合上的批次效应校正表现为：平均iLISI分数为1.917，kBET接受率为0.324。这表明MultiVI能够有效整合来自同一供体不同时间点的数据，实现良好的批次混合。图\ref{fig:batch_s1d1_s1d2_s1d3}展示了该批次组合的UMAP聚类结果。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/batch_s1d1_s1d2_s1d3_umap.png}
\caption{批次组合s1d1\_s1d2\_s1d3的UMAP聚类可视化结果}
\label{fig:batch_s1d1_s1d2_s1d3}
\end{figure}

\textbf{批次组合2（s1d1\_s2d1\_s4d1）}：来自不同供体同一时间点的批次，包含18,467个细胞。该组合的批次效应校正结果为：平均iLISI分数为1.276，kBET接受率为0.025。相对较低的指标值反映了不同供体间的生物学差异，这种差异在一定程度上应当被保留。图\ref{fig:batch_s1d1_s2d1_s4d1}展示了该批次组合的UMAP聚类结果。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/batch_s1d1_s2d1_s4d1_umap.png}
\caption{批次组合s1d1\_s2d1\_s4d1的UMAP聚类可视化结果}
\label{fig:batch_s1d1_s2d1_s4d1}
\end{figure}

\textbf{批次组合3（s4d1\_s4d8\_s4d9）}：来自同一供体不同发育阶段的批次，包含22,224个细胞。MultiVI在该组合上取得了良好的批次校正效果：平均iLISI分数为1.874，kBET接受率为0.341。这表明算法能够有效处理发育时间序列中的批次效应。图\ref{fig:batch_s4d1_s4d8_s4d9}展示了该批次组合的UMAP聚类结果。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/batch_s4d1_s4d8_s4d9_umap.png}
\caption{批次组合s4d1\_s4d8\_s4d9的UMAP聚类可视化结果}
\label{fig:batch_s4d1_s4d8_s4d9}
\end{figure}

表\ref{tab:batch_effect}总结了MultiVI在neurips数据集不同批次组合上的批次效应校正性能。

\input{tables/table_batch_effect}

从图\ref{fig:batch_s1d1_s1d2_s1d3}、图\ref{fig:batch_s1d1_s2d1_s4d1}和图\ref{fig:batch_s4d1_s4d8_s4d9}的UMAP可视化结果可以看出，MultiVI能够有效地将不同批次的细胞进行整合，同时保持细胞类型的清晰分离。特别是在同供体不同时间点的批次组合中，不同批次的细胞实现了良好的混合，表明算法成功消除了技术性批次效应。

总体而言，MultiVI在neurips数据集上展现出良好的批次效应校正能力，特别是在处理技术性批次效应方面表现优异，同时能够适当保留生物学相关的批次间差异。

\subsection{算法对比}

在进行算法对比时，我发现MultiVI确实在大多数测试场景中展现出了优异的性能。为了确保比较的公平性，我构建了一个统一的评估框架，表\ref{tab:algorithm_comparison}详细展示了MultiVI与其他几种主流算法在聚类准确性和计算效率方面的对比结果。

\input{tables/table_2}

从表\ref{tab:algorithm_comparison}可以看出：在聚类准确性方面，MultiVI在两个数据集上都取得了最高的ARI分数，分别为0.854（10x\_lymph\_node）和0.921（paired\_cellline），显著优于其他算法；在计算效率方面，MultiVI在不同规模数据集上都表现出良好的效率，paired\_cellline数据集训练时间为5.7分钟，10x\_lymph\_node数据集训练时间为26.8分钟，考虑到其优异的性能表现，这种时间成本是可以接受的。

MultiVI在细胞类型分离方面表现优异。

\section{讨论}

\subsection{MultiVI的优势}

首先，在细胞类型聚类方面，MultiVI表现最佳，特别是在处理配对数据时。从表\ref{tab:algorithm_comparison}可以看出，MultiVI在ARI指标上显著优于其他算法，能够清晰地分离不同的细胞类型，形成紧密且分离良好的聚类。其次，在批次效应校正方面，MultiVI在neurips数据集上展现出良好的性能。表\ref{tab:batch_effect}显示，MultiVI能够有效处理不同类型的批次效应，特别是在同供体不同时间点和同供体不同发育阶段的场景下表现优异。再次，在参数优化方面，图\ref{fig:10x_metrics}、图\ref{fig:paired_cellline_metrics}和图\ref{fig:snare_metrics}展示了MultiVI在不同数据集上的参数敏感性分析，证明了算法在各种分辨率参数下的稳定性。最后，MultiVI能够处理配对和非配对数据，具有更广泛的适用性，在计算效率方面也表现出色。

\subsection{MultiVI的局限性}

MultiVI也存在一些局限性：计算资源需求较高，特别是在处理大规模数据集时需要GPU加速；训练时间较长，相比矩阵分解方法如MOFA+和scAI；对超参数较为敏感，需要针对不同数据集进行调优。