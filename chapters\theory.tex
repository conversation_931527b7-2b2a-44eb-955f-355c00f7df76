\chapter{相关理论与技术}

\section{单细胞多组学数据特征}

在深入研究MultiVI之前，我觉得有必要先聊聊单细胞多组学数据的一些"个性"。说实话，刚开始接触这类数据时，我被它们的复杂性给震撼了。这些数据有几个非常突出的特点，也正是这些特点让数据集成变得如此具有挑战性：

\subsection{高维性与稀疏性}
第一个让我印象深刻的特点就是数据的高维性。想象一下，一个scRNA-seq数据集可能轻松包含2万多个基因，而scATAC-seq数据更夸张，染色质区域数量可能达到几十万甚至上百万。但更有意思的是，这些数据同时又极其稀疏——大部分位置都是零值。我在处理数据时发现，scRNA-seq数据中零值的比例通常在80-95%之间，而scATAC-seq数据的稀疏性更是达到了99%的程度。这种"又大又空"的特性让传统的分析方法显得力不从心，也是为什么我们需要专门针对这类数据设计新算法的原因。

\subsection{模态间异质性}
另一个让我头疼的问题是不同模态数据之间的巨大差异。举个例子，scRNA-seq数据基本上都是一些离散的计数值，反映的是基因表达的强弱，我通常用负二项分布来建模，主要是为了处理数据中的过度分散现象。而scATAC-seq数据就完全不一样了，它更像是一堆开关状态（开放或关闭），反映染色质的可及性，用伯努利分布或二项分布来描述比较合适。如果再加上蛋白质表达数据，那就更复杂了，因为这类数据往往有很高的背景噪声，需要用混合分布来区分真实信号和背景干扰。

正是因为这种天然的异质性，想要直接把不同模态的数据简单粗暴地合并在一起分析基本上是不可能的，必须要有专门的统计模型来"翻译"各种模态的特殊语言。

\subsection{批次效应}
批次效应这个问题真的是让人又爱又恨。在我处理数据的过程中，经常会遇到这样的情况：明明是同一种细胞类型，但因为来自不同的实验批次，在数据上看起来却差异很大。这些差异可能来自样本处理的细微差别、测序深度的不一致、甚至是不同实验室的操作习惯。最麻烦的是，这些技术性的变异往往会掩盖我们真正关心的生物学差异，所以在数据集成的时候必须想办法把它们"校正"掉。

\subsection{配对与非配对数据共存}
在实际研究中，我发现数据集的结构往往比理论上复杂得多。通常会遇到两种类型的细胞：一种是"幸运儿"——配对细胞，它们通过像10X Multiome这样的技术同时获得了RNA和ATAC的信息；另一种是"单身汉"——非配对细胞，只有单一模态的数据，要么只有RNA-seq，要么只有ATAC-seq。

这种混合的数据结构就像一幅不完整的拼图，有些地方信息很丰富，有些地方却有缺失。这无疑增加了数据集成的难度，但也正是这种挑战让MultiVI这样的算法显得特别有价值——它能够同时处理这两种数据，最大化利用所有可用的信息。

\section{数据集成方法分类}

根据集成策略和处理的数据类型，单细胞数据集成方法可以进行多种分类。

\subsection{按数据结构分类}

根据 MultiVI 论文中的分类，集成算法可以基于其处理共享细胞（垂直集成）、共享特征（水平集成）或两者的子集（马赛克集成）的能力进行分类。

垂直集成主要处理同一组细胞上测量了不同模态的数据（配对数据），例如分析通过10X Multiome技术获取的同一细胞的基因表达和染色质可及性数据，许多早期的多模态分析工具主要聚焦于此类配对数据。水平集成主要处理在不同细胞群体上测量了相同模态数据，但可能存在共享特征子集的情况，例如整合来自不同实验室使用不同scRNA-seq技术生成的数据集，批次校正算法通常属于这一类。马赛克集成处理最复杂的场景，数据集中细胞和特征的测量在不同模态之间可能只有部分重叠，即数据可能包含配对的多模态细胞、单模态细胞，甚至不同单模态细胞（例如，一个数据集只有RNA-seq，另一个只有ATAC-seq），MultiVI的设计目标之一就是处理这种马赛克集成场景，特别是整合配对数据与单模态数据。

\subsection{按核心技术分类}

根据核心技术原理，单细胞多组学数据集成方法主要包括基于降维和对齐的方法、基于矩阵分解的方法、基于图的方法以及基于深度学习和生成模型的方法。其中，基于深度学习的方法利用神经网络强大的特征学习能力和非线性建模能力，通过变分自编码器等生成模型学习数据的联合潜在表示，并通常具备数据去噪和推断的能力。MultiVI属于基于深度学习和生成模型的方法，专为处理马赛克集成场景而设计，特别擅长整合配对数据和单模态数据。

\section{MultiVI 算法原理}

\subsection{变分自编码器基础}
要搞懂MultiVI，首先得理解它的"老祖宗"——变分自编码器（VAE）。说实话，刚开始学VAE的时候我也是一头雾水，但慢慢琢磨之后发现这个框架真的很巧妙。简单来说，VAE就是一个由编码器和解码器组成的生成模型，编码器负责把输入数据"压缩"到一个潜在空间的概率分布里，解码器则从这个潜在空间"解压"出原始数据。整个训练过程的目标就是最大化变分下界（ELBO），这个目标函数包含了重构项和正则化项两个部分。

VAE的核心思想其实挺有意思的，它通过变分推断来近似后验分布$p(z|x)$，这里$z$是潜在变量，$x$是我们观测到的数据。具体来说，VAE用一个编码器网络$q_\phi(z|x)$来近似这个后验分布，然后用解码器网络$p_\theta(x|z)$从潜在变量重构出原始数据。整个训练过程就是要最大化证据下界（ELBO）：

\begin{equation}
\mathcal{L}(\theta, \phi; x) = \mathbb{E}_{q_\phi(z|x)}[\log p_\theta(x|z)] - D_{KL}(q_\phi(z|x) || p(z))
\label{eq:vae-elbo}
\end{equation}

其中式（\ref{eq:vae-elbo}）中第一项是重构项，衡量模型重构数据的能力；第二项是 KL 散度正则化项，确保编码器输出的分布接近先验分布（通常是标准正态分布）。

\subsection{MultiVI 模型结构}
MultiVI 扩展了标准 VAE，具有以下关键组件：

\subsubsection{编码器}
为每种模态（如 RNA、ATAC）设计独立的编码器网络，将高维输入数据映射到潜在空间的概率分布。对于配对数据，所有可用模态的编码器都会被使用；对于非配对数据，只使用可用模态的编码器。

具体来说，对于每个细胞 $c$ 和每个模态（如基因表达 $X^R$ 和染色质可及性 $X^A$），MultiVI 使用模态特定的深度神经网络编码器来学习给定观察数据（$X^R$, $X^A$）和样本/批次信息（$S$）后，细胞潜在状态的后验分布近似 $q(z^R | X^R, S)$ 和 $q(z^A | X^A, S)$。这些后验分布被建模为批次校正的多变量正态分布。

\subsubsection{潜在空间整合}
通过对模态特定的潜在分布进行对齐（使用对称 KL 散度作为惩罚项），实现不同模态潜在表示的整合，创建一个联合潜在空间。

为了获得反映所有模态的联合潜在空间，MultiVI 惩罚模态特定潜在表示（$z^R$, $z^A$）之间的距离，最小化它们之间的差异。具体做法是，在损失函数中加入一个正则项，例如两个分布之间的对称 KL 散度 $\text{symmKL}(q(z^A), q(z^R))$。对于三种模态，惩罚项扩展为所有两两组合的对称 KL 散度之和。

最终的集成细胞状态表示 $q(z | X^R, X^A, S)$ 被估计为模态特定表示的平均值。对于只有单一模态数据可用的细胞（非配对），其潜在状态直接从该可用模态的编码器表示中获取（例如，只有 RNA 数据则使用 $z^R$）。

\subsubsection{解码器}
从联合潜在表示生成原始数据的概率分布参数：scRNA-seq解码生成负二项分布参数，考虑细胞文库大小和基因离散度；scATAC-seq解码生成伯努利分布参数，考虑细胞可及性和区域偏差；蛋白质解码（如适用）生成混合负二项分布参数，区分背景和前景信号。

给定联合潜在表示 $z$，MultiVI 使用模态特定的深度神经网络解码器来生成对原始观察数据的重构或概率估计。

对于基因表达数据 ($X^R$)，从联合潜在表示 $z$ 生成的参数用于定义一个负二项分布（Negative Binomial, NegBin）。这与 scVI 的处理类似，建模离散的计数和基因特异的离散度。

对于染色质可及性数据 ($X^A$)，从联合潜在表示 $z$ 生成的参数用于定义一个 Bernoulli 分布（Ber）。这与 PeakVI 的处理类似，建模二值的可及状态，并考虑区域特异的偏差。

对于蛋白质丰度数据 ($X^P$)，MultiVI 使用一个混合负二项分布来建模，以区分背景和前景蛋白表达信号。这与 totalVI 的处理类似。

对于配对（多模态）细胞，似然函数计算自所有模态的概率模型；对于非配对细胞，似然函数仅计算自其可用模态的概率模型。

\subsection{训练与优化}
模型通过最大化变分下界（ELBO）进行训练。ELBO 包含几个项：对观察数据的重构似然（衡量解码器重构数据的准确性）、潜在变量 $z$ 相对于其先验分布（标准正态分布或 GMM 先验）的 KL 散度（正则化潜在空间），以及惩罚模态特定表示之间差异的对齐正则项。

训练过程中还包含一个对抗性成分，如果来自不同模态的细胞在潜在空间中过度分离，则会受到惩罚，这有助于促进模态间的混合和批次校正。MultiVI 使用一个分类器来预测细胞所属的批次，并通过对抗训练最小化该分类器的性能，从而消除批次信息在潜在空间中的影响。

MultiVI 采用了多种技术防止过拟合，例如 Dropout 层和基于验证集的早停策略。

\subsection{推断能力}
MultiVI 的生成特性是其重要优势，它允许用户不仅获得联合的低维表示，还能够推断每个细胞在高维特征空间中所有模态（包括缺失模态）的归一化、批次校正后的值。这些推断值可以用于下游分析，例如在只有 ATAC-seq 数据的细胞群中进行基因表达的差异分析，或者在只有 RNA-seq 数据的细胞群中识别差异可及的染色质区域。

MultiVI 还能估计推断值的不确定性，帮助用户评估推断结果的可靠性。这是通过从潜在空间多次采样并通过解码器生成多个预测来实现的，从而获得预测分布的统计特性（如均值、方差）。

总而言之，MultiVI 通过建立一个灵活的深度生成模型，显式处理不同模态数据的统计特性和稀疏性，并通过对齐模态特定的潜在空间和采用对抗训练，有效地整合配对和单模态数据，同时提供强大的数据推断功能。






