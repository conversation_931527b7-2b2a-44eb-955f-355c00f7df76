\chapter{相关理论与技术}

\section{单细胞多组学数据特征}

单细胞多组学数据具有以下几个显著特征，这些特征也构成了数据集成的主要挑战：

\subsection{高维性与稀疏性}
单细胞数据通常具有极高的维度。例如，scRNA-seq 数据可能包含超过 20,000 个基因，而 scATAC-seq 数据可能包含数十万甚至数百万个染色质区域。同时，这些数据极其稀疏，scRNA-seq 数据中零值比例通常在 80-95\% 之间，而 scATAC-seq 数据的稀疏性更为显著，零值比例可高达 99\%。这种高维稀疏性使得传统的降维和聚类方法效果不佳，需要专门设计的算法来处理。

\subsection{模态间异质性}
不同模态的数据具有本质上不同的统计特性：scRNA-seq数据通常表示为离散计数，反映基因表达水平，常建模为负二项分布，以捕捉过离散性；scATAC-seq数据通常表示为二值数据（开放/关闭）或稀疏计数，反映染色质可及性，常建模为伯努利分布或二项分布；蛋白质表达数据通常具有较高的背景信号，常建模为混合分布，以区分背景和前景信号。

这种模态间的异质性使得直接整合不同模态数据变得困难，需要特定的统计模型来处理各模态的独特特性。

\subsection{批次效应}
单细胞数据常受到技术和生物因素导致的批次效应影响，如样本处理差异、测序深度变化、实验室间差异等。这些非生物学因素引入的变异可能掩盖真实的生物学差异，需要在数据集成过程中进行有效校正。

\subsection{配对与非配对数据共存}
在实际研究中，数据集通常包含两类细胞：配对（多模态）细胞是指同一细胞测量了多种分子特征，如通过10X Multiome技术同时获取RNA和ATAC数据；非配对（单模态）细胞是指只测量了单一模态，如仅有RNA-seq或仅有ATAC-seq数据。

这种马赛克式的数据结构增加了集成的复杂性，理想的集成方法应能同时处理配对和非配对数据，充分利用所有可用信息。

\section{数据集成方法分类}

根据集成策略和处理的数据类型，单细胞数据集成方法可以进行多种分类。

\subsection{按数据结构分类}

根据 MultiVI 论文中的分类，集成算法可以基于其处理共享细胞（垂直集成）、共享特征（水平集成）或两者的子集（马赛克集成）的能力进行分类。

垂直集成主要处理同一组细胞上测量了不同模态的数据（配对数据），例如分析通过10X Multiome技术获取的同一细胞的基因表达和染色质可及性数据，许多早期的多模态分析工具主要聚焦于此类配对数据。水平集成主要处理在不同细胞群体上测量了相同模态数据，但可能存在共享特征子集的情况，例如整合来自不同实验室使用不同scRNA-seq技术生成的数据集，批次校正算法通常属于这一类。马赛克集成处理最复杂的场景，数据集中细胞和特征的测量在不同模态之间可能只有部分重叠，即数据可能包含配对的多模态细胞、单模态细胞，甚至不同单模态细胞（例如，一个数据集只有RNA-seq，另一个只有ATAC-seq），MultiVI的设计目标之一就是处理这种马赛克集成场景，特别是整合配对数据与单模态数据。

\subsection{按核心技术分类}

根据核心技术原理，单细胞多组学数据集成方法主要包括基于降维和对齐的方法、基于矩阵分解的方法、基于图的方法以及基于深度学习和生成模型的方法。其中，基于深度学习的方法利用神经网络强大的特征学习能力和非线性建模能力，通过变分自编码器等生成模型学习数据的联合潜在表示，并通常具备数据去噪和推断的能力。MultiVI属于基于深度学习和生成模型的方法，专为处理马赛克集成场景而设计，特别擅长整合配对数据和单模态数据。

\section{MultiVI 算法原理}

\subsection{变分自编码器基础}
MultiVI 基于变分自编码器（VAE）框架，VAE 是一种生成模型，由编码器和解码器组成。编码器将输入数据映射到潜在空间的概率分布，解码器从潜在空间采样并重构原始数据。训练目标是最大化变分下界（ELBO），包括重构项和正则化项。

变分自编码器的核心思想是通过变分推断近似后验分布 $p(z|x)$，其中 $z$ 是潜在变量，$x$ 是观测数据。VAE 使用编码器网络 $q_\phi(z|x)$ 来近似这个后验分布，并使用解码器网络 $p_\theta(x|z)$ 从潜在变量重构数据。训练目标是最大化证据下界（ELBO）：

\begin{equation}
\mathcal{L}(\theta, \phi; x) = \mathbb{E}_{q_\phi(z|x)}[\log p_\theta(x|z)] - D_{KL}(q_\phi(z|x) || p(z))
\label{eq:vae-elbo}
\end{equation}

其中式（\ref{eq:vae-elbo}）中第一项是重构项，衡量模型重构数据的能力；第二项是 KL 散度正则化项，确保编码器输出的分布接近先验分布（通常是标准正态分布）。

\subsection{MultiVI 模型结构}
MultiVI 扩展了标准 VAE，具有以下关键组件：

\subsubsection{编码器}
为每种模态（如 RNA、ATAC）设计独立的编码器网络，将高维输入数据映射到潜在空间的概率分布。对于配对数据，所有可用模态的编码器都会被使用；对于非配对数据，只使用可用模态的编码器。

具体来说，对于每个细胞 $c$ 和每个模态（如基因表达 $X^R$ 和染色质可及性 $X^A$），MultiVI 使用模态特定的深度神经网络编码器来学习给定观察数据（$X^R$, $X^A$）和样本/批次信息（$S$）后，细胞潜在状态的后验分布近似 $q(z^R | X^R, S)$ 和 $q(z^A | X^A, S)$。这些后验分布被建模为批次校正的多变量正态分布。

\subsubsection{潜在空间整合}
通过对模态特定的潜在分布进行对齐（使用对称 KL 散度作为惩罚项），实现不同模态潜在表示的整合，创建一个联合潜在空间。

为了获得反映所有模态的联合潜在空间，MultiVI 惩罚模态特定潜在表示（$z^R$, $z^A$）之间的距离，最小化它们之间的差异。具体做法是，在损失函数中加入一个正则项，例如两个分布之间的对称 KL 散度 $\text{symmKL}(q(z^A), q(z^R))$。对于三种模态，惩罚项扩展为所有两两组合的对称 KL 散度之和。

最终的集成细胞状态表示 $q(z | X^R, X^A, S)$ 被估计为模态特定表示的平均值。对于只有单一模态数据可用的细胞（非配对），其潜在状态直接从该可用模态的编码器表示中获取（例如，只有 RNA 数据则使用 $z^R$）。

\subsubsection{解码器}
从联合潜在表示生成原始数据的概率分布参数：scRNA-seq解码生成负二项分布参数，考虑细胞文库大小和基因离散度；scATAC-seq解码生成伯努利分布参数，考虑细胞可及性和区域偏差；蛋白质解码（如适用）生成混合负二项分布参数，区分背景和前景信号。

给定联合潜在表示 $z$，MultiVI 使用模态特定的深度神经网络解码器来生成对原始观察数据的重构或概率估计。

对于基因表达数据 ($X^R$)，从联合潜在表示 $z$ 生成的参数用于定义一个负二项分布（Negative Binomial, NegBin）。这与 scVI 的处理类似，建模离散的计数和基因特异的离散度。

对于染色质可及性数据 ($X^A$)，从联合潜在表示 $z$ 生成的参数用于定义一个 Bernoulli 分布（Ber）。这与 PeakVI 的处理类似，建模二值的可及状态，并考虑区域特异的偏差。

对于蛋白质丰度数据 ($X^P$)，MultiVI 使用一个混合负二项分布来建模，以区分背景和前景蛋白表达信号。这与 totalVI 的处理类似。

对于配对（多模态）细胞，似然函数计算自所有模态的概率模型；对于非配对细胞，似然函数仅计算自其可用模态的概率模型。

\subsection{训练与优化}
模型通过最大化变分下界（ELBO）进行训练。ELBO 包含几个项：对观察数据的重构似然（衡量解码器重构数据的准确性）、潜在变量 $z$ 相对于其先验分布（标准正态分布或 GMM 先验）的 KL 散度（正则化潜在空间），以及惩罚模态特定表示之间差异的对齐正则项。

训练过程中还包含一个对抗性成分，如果来自不同模态的细胞在潜在空间中过度分离，则会受到惩罚，这有助于促进模态间的混合和批次校正。MultiVI 使用一个分类器来预测细胞所属的批次，并通过对抗训练最小化该分类器的性能，从而消除批次信息在潜在空间中的影响。

MultiVI 采用了多种技术防止过拟合，例如 Dropout 层和基于验证集的早停策略。

\subsection{推断能力}
MultiVI 的生成特性是其重要优势，它允许用户不仅获得联合的低维表示，还能够推断每个细胞在高维特征空间中所有模态（包括缺失模态）的归一化、批次校正后的值。这些推断值可以用于下游分析，例如在只有 ATAC-seq 数据的细胞群中进行基因表达的差异分析，或者在只有 RNA-seq 数据的细胞群中识别差异可及的染色质区域。

MultiVI 还能估计推断值的不确定性，帮助用户评估推断结果的可靠性。这是通过从潜在空间多次采样并通过解码器生成多个预测来实现的，从而获得预测分布的统计特性（如均值、方差）。

总而言之，MultiVI 通过建立一个灵活的深度生成模型，显式处理不同模态数据的统计特性和稀疏性，并通过对齐模态特定的潜在空间和采用对抗训练，有效地整合配对和单模态数据，同时提供强大的数据推断功能。






